#!/bin/bash

# =============================================================================
# AI文本游戏智能开发环境主控脚本
# =============================================================================
# 版本: 2.0.0
# 作者: AI助手
# 创建时间: $(date '+%Y-%m-%d %H:%M:%S')
# 
# 功能说明：
# 这是一个智能合并的主控脚本，整合了所有开发环境启动功能：
# - 全栈开发环境（前端+后端同时启动）
# - 前端独立开发环境（仅启动前端服务）
# - 后端独立开发环境（仅启动后端服务）
# - 构建模式（前端构建和部署）
# - 停止服务（优雅停止所有开发服务）
# 
# 特色功能：
# - 智能调试日志控制（支持多级日志详细程度）
# - 灵活的配置选项（数据库、认证、端口等）
# - 完善的错误处理和进程管理
# - 用户友好的交互界面和帮助系统
# - 向后兼容性支持
# =============================================================================

set -e

# =============================================================================
# 全局配置变量
# =============================================================================

# 脚本版本和信息
readonly SCRIPT_VERSION="2.0.0"
readonly SCRIPT_NAME="AI文本游戏智能开发环境主控脚本"

# 调试和日志控制
DEBUG_ENABLED=${DEBUG_ENABLED:-false}
VERBOSE_LOGS=${VERBOSE_LOGS:-false}
QUIET_MODE=${QUIET_MODE:-false}
LOG_TIMESTAMP=${LOG_TIMESTAMP:-true}

# 服务配置
FRONTEND_PORT=${FRONTEND_PORT:-3000}
BACKEND_PORT=${BACKEND_PORT:-8080}
DATABASE_MODE=${DATABASE_MODE:-sqlite}  # sqlite, postgres
SKIP_AUTH=${SKIP_AUTH:-true}
ENABLE_REDIS=${ENABLE_REDIS:-false}

# 环境配置
ENVIRONMENT=${ENVIRONMENT:-development}
NODE_ENV=${NODE_ENV:-development}
BUILD_MODE=${BUILD_MODE:-development}  # development, production

# 进程管理
BACKEND_PID_FILE="/tmp/ai-game-backend.pid"
FRONTEND_PID_FILE="/tmp/ai-game-frontend.pid"
PROCESS_TIMEOUT=30

# 其他选项
SKIP_INSTALL=${SKIP_INSTALL:-false}
FORCE_CLEAN=${FORCE_CLEAN:-false}
DRY_RUN=${DRY_RUN:-false}
CONFIG_FILE=${CONFIG_FILE:-""}

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly BOLD='\033[1m'
readonly DIM='\033[2m'
readonly NC='\033[0m' # No Color

# =============================================================================
# 日志输出函数系统
# =============================================================================

# 获取时间戳
get_timestamp() {
    if [ "$LOG_TIMESTAMP" = true ]; then
        date '+%H:%M:%S'
    else
        echo ""
    fi
}

# 静默模式检查
is_quiet() {
    [ "$QUIET_MODE" = true ]
}

# 调试日志输出
print_debug() {
    if [ "$DEBUG_ENABLED" = true ] && ! is_quiet; then
        local timestamp=$(get_timestamp)
        echo -e "${DIM}${PURPLE}[调试]${NC} ${DIM}$timestamp${NC} $1"
    fi
}

# 详细日志输出
print_verbose() {
    if [ "$VERBOSE_LOGS" = true ] && ! is_quiet; then
        local timestamp=$(get_timestamp)
        echo -e "${CYAN}[详细]${NC} ${DIM}$timestamp${NC} $1"
    fi
}

# 基础日志输出函数
print_info() {
    if ! is_quiet; then
        local timestamp=$(get_timestamp)
        echo -e "${BLUE}[信息]${NC} ${DIM}$timestamp${NC} $1"
    fi
}

print_success() {
    if ! is_quiet; then
        local timestamp=$(get_timestamp)
        echo -e "${GREEN}[成功]${NC} ${DIM}$timestamp${NC} $1"
    fi
}

print_warning() {
    local timestamp=$(get_timestamp)
    echo -e "${YELLOW}[警告]${NC} ${DIM}$timestamp${NC} $1" >&2
}

print_error() {
    local timestamp=$(get_timestamp)
    echo -e "${RED}[错误]${NC} ${DIM}$timestamp${NC} $1" >&2
}

print_step() {
    if ! is_quiet; then
        local timestamp=$(get_timestamp)
        echo -e "${BOLD}${PURPLE}[步骤]${NC} ${DIM}$timestamp${NC} $1"
    fi
}

print_frontend() {
    if ! is_quiet; then
        local timestamp=$(get_timestamp)
        echo -e "${CYAN}[前端]${NC} ${DIM}$timestamp${NC} $1"
    fi
}

print_backend() {
    if ! is_quiet; then
        local timestamp=$(get_timestamp)
        echo -e "${PURPLE}[后端]${NC} ${DIM}$timestamp${NC} $1"
    fi
}

# =============================================================================
# 横幅和帮助系统
# =============================================================================

# 显示主横幅
show_main_banner() {
    local mode=$1
    if is_quiet; then return; fi
    
    echo ""
    echo -e "${BOLD}${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BOLD}${CYAN}║                🚀 AI文本游戏智能开发环境                     ║${NC}"
    echo -e "${BOLD}${CYAN}║                                                              ║${NC}"
    
    case $mode in
        "fullstack")
            echo -e "${BOLD}${CYAN}║                    🌟 全栈开发模式                           ║${NC}"
            ;;
        "frontend")
            echo -e "${BOLD}${CYAN}║                    🎨 前端开发模式                           ║${NC}"
            ;;
        "backend")
            echo -e "${BOLD}${CYAN}║                    ⚡ 后端开发模式                           ║${NC}"
            ;;
        "build")
            echo -e "${BOLD}${CYAN}║                    🔨 构建部署模式                           ║${NC}"
            ;;
        "stop")
            echo -e "${BOLD}${CYAN}║                    🛑 服务停止模式                           ║${NC}"
            ;;
    esac
    
    echo -e "${BOLD}${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  🎨 前端: React + TypeScript + Vite + Ant Design            ║${NC}"
    echo -e "${CYAN}║  ⚡ 后端: Go + Gin + GORM + ${DATABASE_MODE^^}                ║${NC}"
    echo -e "${CYAN}║  🔥 热重载: 代码变更自动刷新                                 ║${NC}"
    echo -e "${CYAN}║  🔓 认证: $([ "$SKIP_AUTH" = true ] && echo "跳过模式" || echo "正常模式")                                       ║${NC}"
    echo -e "${CYAN}║  🐛 调试: $([ "$DEBUG_ENABLED" = true ] && echo "调试模式" || echo "标准模式")                                       ║${NC}"
    echo -e "${CYAN}║  📊 数据库: ${DATABASE_MODE^^} $([ "$ENABLE_REDIS" = true ] && echo "+ Redis" || echo "")                                    ║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  ⚠️  仅用于开发测试，请勿在生产环境使用！                    ║${NC}"
    echo -e "${BOLD}${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    
    # 显示当前配置摘要
    print_info "当前配置摘要："
    print_info "  版本: $SCRIPT_VERSION"
    print_info "  模式: $mode"
    print_info "  前端端口: $FRONTEND_PORT"
    print_info "  后端端口: $BACKEND_PORT"
    print_info "  数据库: $DATABASE_MODE"
    print_info "  认证: $([ "$SKIP_AUTH" = true ] && echo "跳过" || echo "启用")"
    print_info "  Redis: $([ "$ENABLE_REDIS" = true ] && echo "启用" || echo "禁用")"
    print_info "  调试: $([ "$DEBUG_ENABLED" = true ] && echo "启用" || echo "禁用")"
    print_info "  详细日志: $([ "$VERBOSE_LOGS" = true ] && echo "启用" || echo "禁用")"
    echo ""
}

# 显示版本信息
show_version() {
    echo "$SCRIPT_NAME"
    echo "版本: $SCRIPT_VERSION"
    echo "兼容性: 向后兼容所有原有启动脚本"
    echo ""
    echo "整合的原始脚本："
    echo "  - dev_full_stack.sh (全栈启动)"
    echo "  - dev_full_stack_verbose.sh (详细日志全栈)"
    echo "  - dev_frontend.sh (前端启动)"
    echo "  - dev_frontend_only.sh (仅前端)"
    echo "  - dev_no_auth.sh (跳过认证)"
    echo "  - dev_start.sh (基础启动)"
    echo "  - dev_with_sqlite.sh (SQLite模式)"
    echo "  - build_frontend.sh (前端构建)"
    echo "  - stop_dev_servers.sh (停止服务)"
}

# 显示详细帮助信息
show_help() {
    cat << 'EOF'
AI文本游戏智能开发环境主控脚本

用法: ./scripts/dev_master.sh [模式] [选项]

═══════════════════════════════════════════════════════════════════════════════

🚀 启动模式:
  fullstack, full, f     启动完整的全栈开发环境（前端+后端）
  frontend, front, fe    仅启动前端开发服务器
  backend, back, be      仅启动后端开发服务器
  build, b               构建前端应用（生产模式）
  stop, s                停止所有开发服务器

📊 调试选项:
  --debug, -d            启用调试日志输出
  --verbose, -v          启用详细日志输出
  --quiet, -q            静默模式，最小化输出
  --no-timestamp         禁用日志时间戳

🔧 服务配置:
  --frontend-port PORT   设置前端端口（默认: 3000）
  --backend-port PORT    设置后端端口（默认: 8080）
  --timeout SECONDS      设置进程启动超时时间（默认: 30）

🗄️ 数据库选项:
  --sqlite               使用SQLite数据库（默认）
  --postgres             使用PostgreSQL数据库
  --redis                启用Redis缓存服务
  --no-redis             禁用Redis缓存服务

🔐 认证选项:
  --no-auth              跳过身份认证（默认，适合开发）
  --auth                 启用正常身份认证
  --dev-user USER        设置开发模式默认用户

🌍 环境选项:
  --env ENV              设置环境模式（development/production）
  --build-mode MODE      设置构建模式（development/production）

⚙️ 其他选项:
  --no-install           跳过依赖安装检查
  --force-clean          强制清理端口和进程
  --config FILE          使用指定的配置文件
  --dry-run              仅显示将要执行的操作，不实际执行

ℹ️ 信息选项:
  --help, -h             显示此帮助信息
  --version              显示版本信息
  --compatibility        显示向后兼容性信息

═══════════════════════════════════════════════════════════════════════════════

📝 使用示例:

  基础使用:
    ./scripts/dev_master.sh fullstack          # 启动全栈环境
    ./scripts/dev_master.sh frontend           # 仅启动前端
    ./scripts/dev_master.sh backend            # 仅启动后端

  调试模式:
    ./scripts/dev_master.sh fullstack --debug  # 启用调试日志
    ./scripts/dev_master.sh frontend -v        # 启用详细日志
    ./scripts/dev_master.sh backend -q         # 静默模式

  数据库配置:
    ./scripts/dev_master.sh fullstack --postgres --redis
    ./scripts/dev_master.sh backend --sqlite --no-redis

  端口配置:
    ./scripts/dev_master.sh fullstack --frontend-port 3001 --backend-port 8081

  认证配置:
    ./scripts/dev_master.sh fullstack --auth   # 启用认证
    ./scripts/dev_master.sh fullstack --no-auth # 跳过认证

  构建部署:
    ./scripts/dev_master.sh build              # 开发构建
    ./scripts/dev_master.sh build --env production # 生产构建

  服务管理:
    ./scripts/dev_master.sh stop               # 停止所有服务

  环境变量控制:
    DEBUG_ENABLED=true ./scripts/dev_master.sh fullstack
    FRONTEND_PORT=3001 ./scripts/dev_master.sh frontend
    DATABASE_MODE=postgres ./scripts/dev_master.sh backend

═══════════════════════════════════════════════════════════════════════════════

🔄 向后兼容性:

  原脚本                    等效的新命令
  ─────────────────────────────────────────────────────────────────────────────
  dev_full_stack.sh         ./scripts/dev_master.sh fullstack
  dev_full_stack_verbose.sh ./scripts/dev_master.sh fullstack --verbose
  dev_frontend.sh           ./scripts/dev_master.sh frontend
  dev_frontend_only.sh      ./scripts/dev_master.sh frontend
  dev_no_auth.sh            ./scripts/dev_master.sh fullstack --no-auth
  dev_start.sh              ./scripts/dev_master.sh fullstack
  dev_with_sqlite.sh        ./scripts/dev_master.sh fullstack --sqlite
  build_frontend.sh         ./scripts/dev_master.sh build
  stop_dev_servers.sh       ./scripts/dev_master.sh stop

═══════════════════════════════════════════════════════════════════════════════

🛠️ 故障排除:

  端口被占用:
    脚本会自动检测并提供解决方案

  依赖问题:
    使用 --no-install 跳过依赖检查
    或手动运行: go mod tidy && npm install

  进程残留:
    使用 --force-clean 强制清理
    或手动运行: ./scripts/dev_master.sh stop

  配置问题:
    使用 --dry-run 查看将要执行的操作
    检查环境变量和配置文件

═══════════════════════════════════════════════════════════════════════════════

📚 更多信息:
  - 项目文档: docs/
  - 配置指南: docs/统一启动脚本使用指南.md
  - 故障排除: docs/开发环境配置指南.md

EOF
}

# 显示向后兼容性信息
show_compatibility() {
    cat << 'EOF'
向后兼容性信息

本脚本完全兼容所有原有的启动脚本功能，您可以：

1. 继续使用原有脚本（它们仍然存在）
2. 使用新的统一脚本替代原有脚本
3. 逐步迁移到新脚本

迁移映射表:
┌─────────────────────────┬─────────────────────────────────────────┐
│ 原脚本                  │ 新命令                                  │
├─────────────────────────┼─────────────────────────────────────────┤
│ dev_full_stack.sh       │ ./scripts/dev_master.sh fullstack      │
│ dev_full_stack_verbose.sh│ ./scripts/dev_master.sh fullstack -v   │
│ dev_frontend.sh         │ ./scripts/dev_master.sh frontend       │
│ dev_frontend_only.sh    │ ./scripts/dev_master.sh frontend       │
│ dev_no_auth.sh          │ ./scripts/dev_master.sh fullstack --no-auth │
│ dev_start.sh            │ ./scripts/dev_master.sh fullstack      │
│ dev_with_sqlite.sh      │ ./scripts/dev_master.sh fullstack --sqlite │
│ build_frontend.sh       │ ./scripts/dev_master.sh build          │
│ stop_dev_servers.sh     │ ./scripts/dev_master.sh stop           │
└─────────────────────────┴─────────────────────────────────────────┘

建议的迁移步骤:
1. 先使用新脚本测试您的常用场景
2. 确认功能正常后，更新您的开发文档
3. 可选择性地删除不再需要的原脚本

EOF
}

# =============================================================================
# 环境检查和初始化函数
# =============================================================================

# 检查项目根目录
check_project_root() {
    print_debug "检查项目根目录..."

    if [ ! -f "go.mod" ] || [ ! -d "web/frontend" ]; then
        print_error "请在项目根目录下运行此脚本"
        print_info "当前目录: $(pwd)"
        print_info "期望文件: go.mod, web/frontend/"
        exit 1
    fi

    print_debug "项目根目录检查通过"
}

# 检查开发环境依赖
check_prerequisites() {
    print_step "检查开发环境依赖..."

    local missing_deps=()

    # 检查 Go 环境
    if ! command -v go &> /dev/null; then
        missing_deps+=("Go 1.19+")
        print_error "Go 未安装，请先安装 Go 1.19+"
    else
        local go_version=$(go version | cut -d' ' -f3)
        print_debug "Go 版本: $go_version"
    fi

    # 检查 Node.js 环境
    if ! command -v node &> /dev/null; then
        missing_deps+=("Node.js 16+")
        print_error "Node.js 未安装，请先安装 Node.js 16+"
    else
        local node_version=$(node --version)
        print_debug "Node.js 版本: $node_version"

        # 检查版本是否满足要求
        local node_major=$(echo $node_version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$node_major" -lt 16 ]; then
            missing_deps+=("Node.js 16+ (当前: $node_version)")
            print_error "Node.js 版本过低，需要 16+，当前版本: $node_version"
        fi
    fi

    # 检查 npm
    if ! command -v npm &> /dev/null; then
        missing_deps+=("npm")
        print_error "npm 未安装，请先安装 npm"
    else
        local npm_version=$(npm --version)
        print_debug "npm 版本: $npm_version"
    fi

    # 检查可选依赖
    if [ "$DATABASE_MODE" = "postgres" ]; then
        if ! command -v psql &> /dev/null; then
            print_warning "PostgreSQL 客户端未安装，可能影响数据库连接测试"
        fi
    fi

    if [ "$ENABLE_REDIS" = true ]; then
        if ! command -v redis-cli &> /dev/null; then
            print_warning "Redis 客户端未安装，可能影响缓存连接测试"
        fi
    fi

    # 检查结果
    if [ ${#missing_deps[@]} -gt 0 ]; then
        print_error "缺少必要依赖: ${missing_deps[*]}"
        print_info "请安装缺少的依赖后重试"
        exit 1
    fi

    print_success "开发环境依赖检查通过"
}

# 检查和清理端口占用
check_and_clean_ports() {
    print_step "检查端口占用情况..."

    local ports_to_check=()
    local mode=$1

    # 根据模式确定需要检查的端口
    case $mode in
        "fullstack")
            ports_to_check=($FRONTEND_PORT $BACKEND_PORT)
            ;;
        "frontend")
            ports_to_check=($FRONTEND_PORT)
            ;;
        "backend")
            ports_to_check=($BACKEND_PORT)
            ;;
        *)
            return 0
            ;;
    esac

    local occupied_ports=()

    # 检查端口占用
    for port in "${ports_to_check[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            occupied_ports+=($port)
            print_debug "端口 $port 被占用"
        fi
    done

    # 处理端口占用
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        print_warning "以下端口被占用: ${occupied_ports[*]}"

        if [ "$FORCE_CLEAN" = true ]; then
            print_info "强制清理模式，自动停止占用进程..."
            for port in "${occupied_ports[@]}"; do
                print_info "停止端口 $port 的进程..."
                lsof -ti:$port | xargs kill -9 2>/dev/null || true
            done
            sleep 2
            print_success "端口清理完成"
        else
            echo ""
            echo "占用情况详情:"
            for port in "${occupied_ports[@]}"; do
                echo "  端口 $port:"
                lsof -Pi :$port -sTCP:LISTEN | head -2 | sed 's/^/    /'
            done
            echo ""

            if ! is_quiet; then
                read -p "是否停止占用进程并继续？(y/N): " -n 1 -r
                echo ""
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    for port in "${occupied_ports[@]}"; do
                        print_info "停止端口 $port 的进程..."
                        lsof -ti:$port | xargs kill -9 2>/dev/null || true
                    done
                    sleep 2
                    print_success "端口清理完成"
                else
                    print_error "用户取消操作"
                    exit 1
                fi
            else
                print_error "静默模式下无法处理端口占用，请使用 --force-clean 选项"
                exit 1
            fi
        fi
    else
        print_success "端口检查通过"
    fi
}

# 安装和检查依赖
install_dependencies() {
    if [ "$SKIP_INSTALL" = true ]; then
        print_debug "跳过依赖安装检查"
        return 0
    fi

    print_step "检查并安装依赖..."

    # 检查和安装Go依赖
    print_verbose "检查Go模块依赖..."
    if [ ! -f "go.sum" ] || [ "go.mod" -nt "go.sum" ]; then
        print_info "更新Go模块依赖..."
        go mod download
        go mod tidy
        print_success "Go依赖更新完成"
    else
        print_debug "Go依赖已是最新"
    fi

    # 检查和安装前端依赖
    if [ "$1" = "fullstack" ] || [ "$1" = "frontend" ] || [ "$1" = "build" ]; then
        print_verbose "检查前端依赖..."
        cd web/frontend

        if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
            print_info "安装前端依赖..."
            npm install
            print_success "前端依赖安装完成"
        else
            print_debug "前端依赖已是最新"
        fi

        cd ../..
    fi

    print_success "依赖检查完成"
}

# 设置环境变量
setup_environment() {
    local mode=$1
    print_debug "设置环境变量..."

    # 基础环境变量
    export ENVIRONMENT=$ENVIRONMENT
    export NODE_ENV=$NODE_ENV

    # 服务配置
    export FRONTEND_PORT=$FRONTEND_PORT
    export BACKEND_PORT=$BACKEND_PORT

    # 数据库配置
    export DATABASE_MODE=$DATABASE_MODE
    export SKIP_AUTH=$SKIP_AUTH
    export ENABLE_REDIS=$ENABLE_REDIS

    # 调试配置
    export DEV_ENABLE_DEBUG_LOGS=$DEBUG_ENABLED
    export VERBOSE_LOGS=$VERBOSE_LOGS

    # 前端特定环境变量
    if [ "$mode" = "fullstack" ] || [ "$mode" = "frontend" ] || [ "$mode" = "build" ]; then
        export VITE_DEV_MODE=true
        export VITE_SHOW_DEV_INDICATOR=true
        export VITE_API_BASE_URL="http://localhost:$BACKEND_PORT"
        export VITE_DEBUG_ENABLED=$DEBUG_ENABLED
    fi

    # 构建模式特定配置
    if [ "$mode" = "build" ]; then
        export NODE_ENV=$BUILD_MODE
        export VITE_BUILD_MODE=$BUILD_MODE
    fi

    print_debug "环境变量设置完成"

    if [ "$VERBOSE_LOGS" = true ]; then
        print_verbose "当前环境变量:"
        print_verbose "  ENVIRONMENT=$ENVIRONMENT"
        print_verbose "  DATABASE_MODE=$DATABASE_MODE"
        print_verbose "  SKIP_AUTH=$SKIP_AUTH"
        print_verbose "  FRONTEND_PORT=$FRONTEND_PORT"
        print_verbose "  BACKEND_PORT=$BACKEND_PORT"
    fi
}

# =============================================================================
# 服务启动和管理函数
# =============================================================================

# 启动后端服务器
start_backend_service() {
    print_backend "启动后端开发服务器..."

    # 选择合适的服务器启动命令
    local server_cmd
    if [ "$SKIP_AUTH" = true ]; then
        server_cmd="cmd/simple-server/main.go"
        print_backend "使用简化版服务器（认证跳过模式）"
    else
        server_cmd="cmd/server/main.go"
        print_backend "使用完整版服务器（正常认证模式）"
    fi

    print_debug "后端服务器配置:"
    print_debug "  命令: go run $server_cmd"
    print_debug "  端口: $BACKEND_PORT"
    print_debug "  数据库: $DATABASE_MODE"
    print_debug "  认证: $([ "$SKIP_AUTH" = true ] && echo "跳过" || echo "启用")"

    # 启动后端服务器
    print_backend "启动后端服务器，端口: $BACKEND_PORT"

    if [ "$DEBUG_ENABLED" = true ] || [ "$VERBOSE_LOGS" = true ]; then
        # 调试模式下显示实时日志
        print_backend "调试模式：显示实时日志"
        go run $server_cmd 2>&1 | while IFS= read -r line; do
            echo -e "${PURPLE}[后端]${NC} $line"
        done &
    else
        # 标准模式下后台运行
        print_backend "标准模式：后台运行，日志保存到文件"
        nohup go run $server_cmd > /tmp/backend.log 2>&1 &
    fi

    local backend_pid=$!
    echo $backend_pid > "$BACKEND_PID_FILE"
    print_debug "后端进程 PID: $backend_pid"

    # 等待后端启动
    print_backend "等待后端服务器启动..."
    local max_attempts=$((PROCESS_TIMEOUT / 2))
    local attempt=1

    sleep 3  # 给后端一些启动时间

    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:$BACKEND_PORT/health > /dev/null 2>&1; then
            print_success "后端服务器启动成功 (PID: $backend_pid)"

            # 检查开发模式状态
            local health_response=$(curl -s http://localhost:$BACKEND_PORT/health 2>/dev/null || echo "{}")
            if echo "$health_response" | grep -q '"auth_mode":"disabled"'; then
                print_success "后端开发模式已启用，认证已跳过"
            fi

            print_debug "后端健康检查响应: $health_response"
            return 0
        fi

        print_verbose "等待后端启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done

    print_error "后端服务器启动超时"
    if [ -f "/tmp/backend.log" ]; then
        print_error "后端日志（最后20行）："
        tail -20 /tmp/backend.log | sed 's/^/  /'
    fi
    return 1
}

# 启动前端服务器
start_frontend_service() {
    print_frontend "启动前端开发服务器..."

    cd web/frontend

    print_debug "前端服务器配置:"
    print_debug "  端口: $FRONTEND_PORT"
    print_debug "  API代理: http://localhost:$BACKEND_PORT"
    print_debug "  开发模式: $NODE_ENV"

    # 启动前端服务器
    print_frontend "启动 Vite 开发服务器，端口: $FRONTEND_PORT"

    if [ "$DEBUG_ENABLED" = true ] || [ "$VERBOSE_LOGS" = true ]; then
        # 调试模式下显示实时日志
        print_frontend "调试模式：显示实时日志"
        npm run dev 2>&1 | while IFS= read -r line; do
            echo -e "${CYAN}[前端]${NC} $line"
        done &
    else
        # 标准模式下后台运行
        print_frontend "标准模式：后台运行，日志保存到文件"
        nohup npm run dev > /tmp/frontend.log 2>&1 &
    fi

    local frontend_pid=$!
    echo $frontend_pid > "$FRONTEND_PID_FILE"
    print_debug "前端进程 PID: $frontend_pid"

    cd ../..

    # 等待前端启动
    print_frontend "等待前端服务器启动..."
    local max_attempts=$((PROCESS_TIMEOUT / 2))
    local attempt=1

    sleep 3  # 给前端一些启动时间

    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:$FRONTEND_PORT > /dev/null 2>&1; then
            print_success "前端服务器启动成功 (PID: $frontend_pid)"
            return 0
        fi

        print_verbose "等待前端启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done

    print_warning "前端服务器可能仍在启动中..."
    if [ -f "/tmp/frontend.log" ]; then
        print_warning "前端日志（最后10行）："
        tail -10 /tmp/frontend.log | sed 's/^/  /'
    fi
    return 0
}

# 构建前端应用
build_frontend_app() {
    print_step "构建前端应用..."

    cd web/frontend

    print_info "构建配置:"
    print_info "  构建模式: $BUILD_MODE"
    print_info "  Node环境: $NODE_ENV"
    print_info "  输出目录: dist/"

    # 清理旧的构建产物
    if [ -d "dist" ]; then
        print_verbose "清理旧的构建产物..."
        rm -rf dist/
    fi

    # 执行构建
    print_info "开始构建前端应用..."
    if [ "$VERBOSE_LOGS" = true ]; then
        npm run build
    else
        npm run build > /tmp/build.log 2>&1
    fi

    local build_result=$?

    if [ $build_result -eq 0 ]; then
        print_success "前端构建完成"

        # 显示构建产物信息
        if [ -d "dist" ]; then
            local build_size=$(du -sh dist/ | cut -f1)
            print_info "构建产物大小: $build_size"
            print_info "构建产物位置: web/frontend/dist/"

            if [ "$VERBOSE_LOGS" = true ]; then
                print_verbose "构建产物列表:"
                find dist/ -type f | head -10 | sed 's/^/  /'
                if [ $(find dist/ -type f | wc -l) -gt 10 ]; then
                    print_verbose "  ... 还有更多文件"
                fi
            fi
        fi

        # 可选：复制到静态文件目录
        if [ -d "../static" ]; then
            print_info "复制构建产物到静态文件目录..."
            cp -r dist/* ../static/
            print_success "构建产物已复制到 web/static/"
        fi
    else
        print_error "前端构建失败"
        if [ -f "/tmp/build.log" ]; then
            print_error "构建日志："
            tail -20 /tmp/build.log | sed 's/^/  /'
        fi
        cd ../..
        return 1
    fi

    cd ../..
    return 0
}

# 停止所有开发服务
stop_all_services() {
    print_step "停止所有开发服务..."

    local stopped_services=()
    local failed_services=()

    # 停止后端服务器
    if [ -f "$BACKEND_PID_FILE" ]; then
        local backend_pid=$(cat "$BACKEND_PID_FILE")
        if kill -0 "$backend_pid" 2>/dev/null; then
            print_backend "停止后端服务器 (PID: $backend_pid)..."

            # 尝试优雅停止
            kill "$backend_pid" 2>/dev/null || true

            # 等待进程结束
            local count=0
            while kill -0 "$backend_pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                ((count++))
            done

            # 如果进程仍在运行，强制终止
            if kill -0 "$backend_pid" 2>/dev/null; then
                print_warning "强制终止后端进程..."
                kill -9 "$backend_pid" 2>/dev/null || true
            fi

            stopped_services+=("后端服务器")
        else
            print_debug "后端服务器进程不存在"
        fi
        rm -f "$BACKEND_PID_FILE"
    fi

    # 停止前端服务器
    if [ -f "$FRONTEND_PID_FILE" ]; then
        local frontend_pid=$(cat "$FRONTEND_PID_FILE")
        if kill -0 "$frontend_pid" 2>/dev/null; then
            print_frontend "停止前端服务器 (PID: $frontend_pid)..."

            # 尝试优雅停止
            kill "$frontend_pid" 2>/dev/null || true

            # 等待进程结束
            local count=0
            while kill -0 "$frontend_pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                ((count++))
            done

            # 如果进程仍在运行，强制终止
            if kill -0 "$frontend_pid" 2>/dev/null; then
                print_warning "强制终止前端进程..."
                kill -9 "$frontend_pid" 2>/dev/null || true
            fi

            stopped_services+=("前端服务器")
        else
            print_debug "前端服务器进程不存在"
        fi
        rm -f "$FRONTEND_PID_FILE"
    fi

    # 清理可能残留的进程
    print_debug "清理残留进程..."
    local cleanup_patterns=(
        "go run cmd/simple-server/main.go"
        "go run cmd/server/main.go"
        "npm run dev"
        "vite"
    )

    for pattern in "${cleanup_patterns[@]}"; do
        if pkill -f "$pattern" 2>/dev/null; then
            print_debug "清理进程: $pattern"
        fi
    done

    # 清理临时文件
    print_debug "清理临时文件..."
    rm -f /tmp/backend.log /tmp/frontend.log /tmp/build.log 2>/dev/null || true

    # 显示结果
    if [ ${#stopped_services[@]} -gt 0 ]; then
        print_success "已停止服务: ${stopped_services[*]}"
    else
        print_info "没有运行中的开发服务"
    fi

    if [ ${#failed_services[@]} -gt 0 ]; then
        print_warning "停止失败的服务: ${failed_services[*]}"
        return 1
    fi

    return 0
}

# 显示服务状态信息
show_service_status() {
    local mode=$1

    if is_quiet; then return; fi

    echo ""
    print_success "🚀 AI文本游戏开发环境已启动"
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    echo "🌐 访问地址:"

    case $mode in
        "fullstack"|"frontend")
            echo "   前端应用:      http://localhost:$FRONTEND_PORT"
            echo "   开发工具:      http://localhost:$FRONTEND_PORT (F12)"
            ;;
    esac

    case $mode in
        "fullstack"|"backend")
            echo "   后端API:       http://localhost:$BACKEND_PORT"
            echo "   健康检查:      http://localhost:$BACKEND_PORT/health"
            echo "   API文档:       http://localhost:$BACKEND_PORT/api/docs"
            ;;
    esac

    echo ""
    echo "🔧 开发特性:"
    echo "   ✅ 热重载:       代码变更自动刷新"
    echo "   ✅ 开发工具:     浏览器 F12 + 开发指示器"
    echo "   ✅ 认证模式:     $([ "$SKIP_AUTH" = true ] && echo "跳过认证（开发模式）" || echo "正常认证")"
    echo "   ✅ 数据库:       $DATABASE_MODE 模式"
    echo "   ✅ 缓存服务:     $([ "$ENABLE_REDIS" = true ] && echo "Redis 已启用" || echo "Redis 已禁用")"
    echo "   ✅ 调试日志:     $([ "$DEBUG_ENABLED" = true ] && echo "已启用" || echo "标准模式")"
    echo ""
    echo "🎮 快速开始:"
    if [ "$mode" = "fullstack" ] || [ "$mode" = "frontend" ]; then
        echo "   1. 打开浏览器访问 http://localhost:$FRONTEND_PORT"
        if [ "$SKIP_AUTH" = true ]; then
            echo "   2. 点击 '🚀 开发模式快速登录' 按钮"
        fi
        echo "   3. 点击右上角 DEV 按钮查看开发状态"
        echo "   4. 开始开发和测试功能"
    fi
    echo ""
    echo "🛠️  API测试:"
    if [ "$mode" = "fullstack" ] || [ "$mode" = "backend" ]; then
        echo "   curl http://localhost:$BACKEND_PORT/health"
        echo "   curl http://localhost:$BACKEND_PORT/api/v1/user/profile"
        echo "   curl http://localhost:$BACKEND_PORT/api/v1/game/my-worlds"
    fi
    echo ""
    echo "📝 进程信息:"
    if [ -f "$BACKEND_PID_FILE" ]; then
        echo "   后端进程 PID:   $(cat $BACKEND_PID_FILE)"
    fi
    if [ -f "$FRONTEND_PID_FILE" ]; then
        echo "   前端进程 PID:   $(cat $FRONTEND_PID_FILE)"
    fi
    echo ""
    echo "📋 日志管理:"
    if [ "$DEBUG_ENABLED" = true ] || [ "$VERBOSE_LOGS" = true ]; then
        echo "   实时日志:       已在控制台显示"
    else
        echo "   后端日志:       /tmp/backend.log"
        echo "   前端日志:       /tmp/frontend.log"
        echo "   查看实时日志:   tail -f /tmp/backend.log"
    fi
    echo ""
    echo "🔄 服务管理:"
    echo "   停止服务:       ./scripts/dev_master.sh stop"
    echo "   重启服务:       先停止，再启动"
    echo "   查看状态:       ps aux | grep -E '(go run|npm run)'"
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    print_warning "⚠️  按 Ctrl+C 停止所有服务"
    print_warning "⚠️  此模式仅用于开发测试，请勿在生产环境使用！"
    echo ""
}

# 监控服务状态
monitor_services() {
    local mode=$1

    if [ "$mode" = "build" ] || [ "$mode" = "stop" ]; then
        return 0
    fi

    print_info "开始监控服务状态..."
    print_debug "监控模式: $mode"

    while true; do
        local services_running=true

        # 检查后端服务器
        if [ "$mode" = "fullstack" ] || [ "$mode" = "backend" ]; then
            if [ -f "$BACKEND_PID_FILE" ]; then
                local backend_pid=$(cat "$BACKEND_PID_FILE")
                if ! kill -0 "$backend_pid" 2>/dev/null; then
                    print_error "后端服务器意外停止 (PID: $backend_pid)"
                    if [ -f "/tmp/backend.log" ]; then
                        print_error "后端日志（最后20行）："
                        tail -20 /tmp/backend.log | sed 's/^/  /'
                    fi
                    services_running=false
                fi
            fi
        fi

        # 检查前端服务器
        if [ "$mode" = "fullstack" ] || [ "$mode" = "frontend" ]; then
            if [ -f "$FRONTEND_PID_FILE" ]; then
                local frontend_pid=$(cat "$FRONTEND_PID_FILE")
                if ! kill -0 "$frontend_pid" 2>/dev/null; then
                    print_error "前端服务器意外停止 (PID: $frontend_pid)"
                    if [ -f "/tmp/frontend.log" ]; then
                        print_error "前端日志（最后20行）："
                        tail -20 /tmp/frontend.log | sed 's/^/  /'
                    fi
                    services_running=false
                fi
            fi
        fi

        # 如果有服务停止，执行清理并退出
        if [ "$services_running" = false ]; then
            print_error "检测到服务异常停止，正在清理..."
            stop_all_services
            exit 1
        fi

        # 每5秒检查一次
        sleep 5
    done
}

# =============================================================================
# 清理和信号处理函数
# =============================================================================

# 清理函数（信号处理）
cleanup_on_exit() {
    echo ""
    print_info "接收到退出信号，正在清理..."
    stop_all_services
    exit 0
}

# =============================================================================
# 参数解析和主函数
# =============================================================================

# 解析命令行参数
parse_arguments() {
    local mode=""  # 默认模式为空，后面会设置

    # 如果没有参数，显示帮助
    if [ $# -eq 0 ]; then
        show_help
        exit 0
    fi

    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            # 模式参数
            fullstack|full|f)
                mode="fullstack"
                shift
                ;;
            frontend|front|fe)
                mode="frontend"
                shift
                ;;
            backend|back|be)
                mode="backend"
                shift
                ;;
            build|b)
                mode="build"
                shift
                ;;
            stop|s)
                mode="stop"
                shift
                ;;

            # 调试选项
            --debug|-d)
                DEBUG_ENABLED=true
                shift
                ;;
            --verbose|-v)
                VERBOSE_LOGS=true
                shift
                ;;
            --quiet|-q)
                QUIET_MODE=true
                shift
                ;;
            --no-timestamp)
                LOG_TIMESTAMP=false
                shift
                ;;

            # 服务配置
            --frontend-port)
                FRONTEND_PORT=$2
                shift 2
                ;;
            --backend-port)
                BACKEND_PORT=$2
                shift 2
                ;;
            --timeout)
                PROCESS_TIMEOUT=$2
                shift 2
                ;;

            # 数据库选项
            --sqlite)
                DATABASE_MODE=sqlite
                shift
                ;;
            --postgres)
                DATABASE_MODE=postgres
                shift
                ;;
            --redis)
                ENABLE_REDIS=true
                shift
                ;;
            --no-redis)
                ENABLE_REDIS=false
                shift
                ;;

            # 认证选项
            --no-auth)
                SKIP_AUTH=true
                shift
                ;;
            --auth)
                SKIP_AUTH=false
                shift
                ;;
            --dev-user)
                DEV_USER=$2
                shift 2
                ;;

            # 环境选项
            --env)
                ENVIRONMENT=$2
                NODE_ENV=$2
                shift 2
                ;;
            --build-mode)
                BUILD_MODE=$2
                shift 2
                ;;

            # 其他选项
            --no-install)
                SKIP_INSTALL=true
                shift
                ;;
            --force-clean)
                FORCE_CLEAN=true
                shift
                ;;
            --config)
                CONFIG_FILE=$2
                shift 2
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;

            # 信息选项（这些在main函数开头已处理）
            --help|-h|--version|--compatibility)
                # 这些选项在main函数开头已经处理，这里跳过
                shift
                ;;

            # 未知选项
            *)
                print_error "未知选项: $1"
                echo ""
                echo "使用 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done

    # 如果没有设置模式，默认为fullstack
    if [ -z "$mode" ]; then
        mode="fullstack"
    fi

    echo "$mode"
}

# 执行干运行模式
execute_dry_run() {
    local mode=$1

    echo ""
    echo "🔍 干运行模式 - 将要执行的操作："
    echo ""
    echo "模式: $mode"
    echo "配置:"
    echo "  前端端口: $FRONTEND_PORT"
    echo "  后端端口: $BACKEND_PORT"
    echo "  数据库: $DATABASE_MODE"
    echo "  认证: $([ "$SKIP_AUTH" = true ] && echo "跳过" || echo "启用")"
    echo "  Redis: $([ "$ENABLE_REDIS" = true ] && echo "启用" || echo "禁用")"
    echo "  调试: $([ "$DEBUG_ENABLED" = true ] && echo "启用" || echo "禁用")"
    echo ""
    echo "将要执行的步骤:"

    case $mode in
        "fullstack")
            echo "  1. 检查项目根目录"
            echo "  2. 检查开发环境依赖"
            echo "  3. 检查和清理端口占用 ($FRONTEND_PORT, $BACKEND_PORT)"
            echo "  4. 安装依赖"
            echo "  5. 设置环境变量"
            echo "  6. 启动后端服务器"
            echo "  7. 启动前端服务器"
            echo "  8. 显示服务状态"
            echo "  9. 监控服务运行"
            ;;
        "frontend")
            echo "  1. 检查项目根目录"
            echo "  2. 检查开发环境依赖"
            echo "  3. 检查和清理端口占用 ($FRONTEND_PORT)"
            echo "  4. 安装前端依赖"
            echo "  5. 设置环境变量"
            echo "  6. 启动前端服务器"
            echo "  7. 显示服务状态"
            echo "  8. 监控服务运行"
            ;;
        "backend")
            echo "  1. 检查项目根目录"
            echo "  2. 检查开发环境依赖"
            echo "  3. 检查和清理端口占用 ($BACKEND_PORT)"
            echo "  4. 安装Go依赖"
            echo "  5. 设置环境变量"
            echo "  6. 启动后端服务器"
            echo "  7. 显示服务状态"
            echo "  8. 监控服务运行"
            ;;
        "build")
            echo "  1. 检查项目根目录"
            echo "  2. 检查开发环境依赖"
            echo "  3. 安装前端依赖"
            echo "  4. 设置构建环境变量"
            echo "  5. 构建前端应用"
            echo "  6. 复制构建产物"
            ;;
        "stop")
            echo "  1. 停止后端服务器"
            echo "  2. 停止前端服务器"
            echo "  3. 清理残留进程"
            echo "  4. 清理临时文件"
            ;;
    esac

    echo ""
    echo "注意: 这是干运行模式，实际不会执行任何操作"
    echo "移除 --dry-run 参数来实际执行"
    echo ""
}

# 主函数
main() {
    # 处理信息选项（在参数解析前）
    for arg in "$@"; do
        case $arg in
            --help|-h)
                show_help
                exit 0
                ;;
            --version)
                show_version
                exit 0
                ;;
            --compatibility)
                show_compatibility
                exit 0
                ;;
        esac
    done

    # 解析命令行参数
    local mode=$(parse_arguments "$@")

    # 设置信号处理
    trap cleanup_on_exit SIGINT SIGTERM

    # 干运行模式
    if [ "$DRY_RUN" = true ]; then
        execute_dry_run "$mode"
        exit 0
    fi

    # 显示横幅
    show_main_banner "$mode"

    # 根据模式执行相应操作
    case $mode in
        "stop")
            stop_all_services
            if [ $? -eq 0 ]; then
                print_success "🛑 所有开发服务已停止"
            else
                print_error "停止服务时遇到问题"
                exit 1
            fi
            ;;

        "build")
            print_step "开始构建模式..."
            check_project_root
            check_prerequisites
            install_dependencies "$mode"
            setup_environment "$mode"

            if build_frontend_app; then
                print_success "🔨 前端构建完成"
                echo ""
                print_info "构建产物位置: web/frontend/dist/"
                print_info "可以使用以下命令启动生产服务器:"
                print_info "  cd web/frontend && npm run preview"
            else
                print_error "构建失败"
                exit 1
            fi
            ;;

        "fullstack")
            print_step "开始全栈开发模式..."
            check_project_root
            check_prerequisites
            check_and_clean_ports "$mode"
            install_dependencies "$mode"
            setup_environment "$mode"

            # 启动后端
            if ! start_backend_service; then
                print_error "后端启动失败，停止启动流程"
                stop_all_services
                exit 1
            fi

            # 启动前端
            if ! start_frontend_service; then
                print_error "前端启动失败，但后端仍在运行"
                print_info "您可以手动启动前端或停止所有服务"
            fi

            # 显示状态并开始监控
            show_service_status "$mode"
            monitor_services "$mode"
            ;;

        "frontend")
            print_step "开始前端开发模式..."
            check_project_root
            check_prerequisites
            check_and_clean_ports "$mode"
            install_dependencies "$mode"
            setup_environment "$mode"

            if ! start_frontend_service; then
                print_error "前端启动失败"
                exit 1
            fi

            show_service_status "$mode"
            monitor_services "$mode"
            ;;

        "backend")
            print_step "开始后端开发模式..."
            check_project_root
            check_prerequisites
            check_and_clean_ports "$mode"
            install_dependencies "$mode"
            setup_environment "$mode"

            if ! start_backend_service; then
                print_error "后端启动失败"
                exit 1
            fi

            show_service_status "$mode"
            monitor_services "$mode"
            ;;

        *)
            print_error "未知模式: $mode"
            show_help
            exit 1
            ;;
    esac
}

# =============================================================================
# 脚本入口点
# =============================================================================

# 检查是否在项目根目录（快速检查）
if [ ! -f "go.mod" ] && [ "$1" != "--help" ] && [ "$1" != "-h" ] && [ "$1" != "--version" ] && [ "$1" != "--compatibility" ]; then
    print_error "请在项目根目录下运行此脚本"
    print_info "当前目录: $(pwd)"
    print_info "期望文件: go.mod"
    exit 1
fi

# 运行主函数
main "$@"
